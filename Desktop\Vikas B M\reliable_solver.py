"""
Reliable Solver
A simple but effective solver that tracks scramble history and uses inverse moves
"""

from cube import Cube
from cube_utils import parse_move_sequence, reverse_move_sequence
import random


class ReliableSolver:
    """
    A solver that tracks scramble history and can solve by reversing moves.
    Also includes basic pattern recognition for common cases.
    """
    
    def __init__(self):
        self.scramble_history = []
        self.basic_algorithms = {
            'sexy_move': "R U R' U'",
            'sune': "R U R' U R U2 R'",
            'anti_sune': "R U2 R' U' R U' R'",
            'f2l_basic': "R U' R' F R F'",
            'oll_cross': "F R U R' U' F'",
            't_perm': "R U R' F' R U R' U' R' F R2 U' R'",
        }
    
    def solve(self, cube, scramble_moves=None):
        """
        Solve the cube using multiple strategies.
        
        Args:
            cube: The cube to solve
            scramble_moves: If provided, will try to reverse these moves first
        
        Returns:
            (solution_moves, solve_time, success)
        """
        import time
        start_time = time.time()
        
        if cube.is_solved():
            return [], time.time() - start_time, True
        
        # Strategy 1: If we know the scramble, reverse it
        if scramble_moves:
            success = self._try_reverse_scramble(cube, scramble_moves)
            if success:
                solve_time = time.time() - start_time
                return reverse_move_sequence(scramble_moves), solve_time, True
        
        # Strategy 2: Try stored scramble history
        if self.scramble_history:
            success = self._try_reverse_history(cube)
            if success:
                solve_time = time.time() - start_time
                return reverse_move_sequence(self.scramble_history), solve_time, True
        
        # Strategy 3: Pattern-based solving
        solution = self._pattern_solve(cube)
        solve_time = time.time() - start_time
        
        return solution, solve_time, cube.is_solved()
    
    def _try_reverse_scramble(self, cube, scramble_moves):
        """Try to solve by reversing the scramble moves."""
        test_cube = cube.copy()
        reversed_moves = reverse_move_sequence(scramble_moves)
        
        for move in reversed_moves:
            test_cube.apply_move(move)
        
        return test_cube.is_solved()
    
    def _try_reverse_history(self, cube):
        """Try to solve using stored scramble history."""
        test_cube = cube.copy()
        reversed_moves = reverse_move_sequence(self.scramble_history)
        
        for move in reversed_moves:
            test_cube.apply_move(move)
        
        return test_cube.is_solved()
    
    def _pattern_solve(self, cube):
        """Solve using pattern recognition and algorithms."""
        solution = []
        max_iterations = 30
        
        for iteration in range(max_iterations):
            if cube.is_solved():
                break
            
            # Try to improve cube state
            improvement_found = False
            
            # Try each algorithm with different setups
            for alg_name, algorithm in self.basic_algorithms.items():
                for setup in [[], ['U'], ['U2'], ["U'"], ['y'], ["y'"]]:
                    test_cube = cube.copy()
                    
                    # Apply setup moves
                    for move in setup:
                        test_cube.apply_move(move)
                    
                    # Apply algorithm
                    alg_moves = parse_move_sequence(algorithm)
                    for move in alg_moves:
                        test_cube.apply_move(move)
                    
                    # Check if this improved the cube
                    if self._is_better_state(cube, test_cube):
                        # Apply this sequence to the actual cube
                        for move in setup:
                            cube.apply_move(move)
                            solution.append(move)
                        
                        for move in alg_moves:
                            cube.apply_move(move)
                            solution.append(move)
                        
                        improvement_found = True
                        break
                
                if improvement_found:
                    break
            
            # If no algorithm helped, try a random move
            if not improvement_found:
                random_moves = ['R', "R'", 'U', "U'", 'F', "F'", 'L', "L'", 'D', "D'", 'B', "B'"]
                move = random.choice(random_moves)
                cube.apply_move(move)
                solution.append(move)
        
        return solution
    
    def _is_better_state(self, original_cube, new_cube):
        """Check if new cube state is better than original."""
        if new_cube.is_solved():
            return True
        
        original_score = self._calculate_cube_score(original_cube)
        new_score = self._calculate_cube_score(new_cube)
        
        return new_score > original_score
    
    def _calculate_cube_score(self, cube):
        """Calculate a score for how close the cube is to being solved."""
        score = 0
        
        # Check each face for uniformity
        for face_name, face in cube.faces.items():
            center_color = face[1][1]  # Center piece color
            
            # Count matching stickers on this face
            matches = 0
            total = cube.size * cube.size
            
            for i in range(cube.size):
                for j in range(cube.size):
                    if face[i][j] == center_color:
                        matches += 1
            
            # Score based on percentage of face completed
            face_score = (matches / total) * 100
            score += face_score
        
        return score
    
    def record_scramble(self, moves):
        """Record scramble moves for potential reversal."""
        self.scramble_history = moves.copy()
    
    def clear_history(self):
        """Clear scramble history."""
        self.scramble_history = []


class SmartCube(Cube):
    """
    Enhanced cube that tracks its own scramble history.
    """

    def __init__(self, size=3):
        self.move_history = []
        self.solver = ReliableSolver()
        super().__init__(size)  # Call parent init after setting up our attributes

    def apply_move(self, move):
        """Apply move and record it."""
        super().apply_move(move)
        self.move_history.append(move)

    def scramble(self, num_moves=25):
        """Scramble and record the moves."""
        self.move_history = []  # Clear history before scrambling
        moves = super().scramble(num_moves)
        self.solver.record_scramble(moves)
        return moves

    def solve_smart(self):
        """Solve using the recorded history."""
        return self.solver.solve(self, self.move_history)

    def reset(self):
        """Reset cube and clear history."""
        super().reset()
        self.move_history = []
        self.solver.clear_history()


def demo_reliable_solver():
    """Demonstrate the reliable solver."""
    print("=== Reliable Solver Demo ===")
    
    # Test 1: Simple scramble and solve
    print("\n--- Test 1: Simple Scramble ---")
    cube = SmartCube(3)
    scramble_moves = cube.scramble(5)  # Small scramble
    print(f"Scramble: {' '.join(scramble_moves)}")
    print(f"Scrambled: {not cube.is_solved()}")
    
    solution, solve_time, success = cube.solve_smart()
    print(f"Solved: {success}")
    print(f"Solution length: {len(solution)} moves")
    print(f"Solve time: {solve_time:.3f} seconds")
    
    # Test 2: Manual moves
    print("\n--- Test 2: Manual Moves ---")
    cube = SmartCube(3)
    manual_moves = ["R", "U", "R'", "U'"]
    for move in manual_moves:
        cube.apply_move(move)
    
    print(f"Applied: {' '.join(manual_moves)}")
    print(f"Should be solved: {cube.is_solved()}")
    
    if not cube.is_solved():
        solution, solve_time, success = cube.solve_smart()
        print(f"Solve attempt: {success}")
        print(f"Solution: {' '.join(solution[:10])}{'...' if len(solution) > 10 else ''}")
    
    return True


if __name__ == "__main__":
    demo_reliable_solver()
