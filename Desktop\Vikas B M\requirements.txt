# Core dependencies for the Rubik's Cube Solver
# Python 3.7+ required

# Optional: Kociemba algorithm for optimal 3x3 solving
# Install with: pip install kociemba
# kociemba>=1.2.0

# Note: Tkinter is included with Python standard library
# No additional GUI dependencies required

# For development and testing (optional):
# pytest>=6.0.0
# pytest-cov>=2.10.0

# The project is designed to work with Python standard library only
# Kociemba is the only optional external dependency for optimal solving
