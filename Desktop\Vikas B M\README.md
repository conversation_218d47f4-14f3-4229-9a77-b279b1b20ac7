# Ultimate Rubik's Cube Solver

A comprehensive Python application for solving Rubi<PERSON>'s Cubes of various sizes with multiple algorithms and interactive visualization.

## Features

### 🎯 **Multi-Size Support**
- **2x2 Pocket Cube**: Ortega method
- **3x3 Rubik's Cube**: Kociemba optimal + Beginner layer-by-layer
- **4x4 Revenge's Cube**: Reduction method with parity handling

### 🧠 **Multiple Solving Algorithms**
- **Kociemba Two-Phase Algorithm**: Optimal solutions (≤20 moves) for 3x3 cubes
- **Beginner Layer-by-Layer**: Step-by-step educational method
- **Size-Specific Solvers**: Tailored algorithms for 2x2 and 4x4 cubes

### 🎨 **Interactive Visualization**
- 2D net layout display with color-coded faces
- Real-time cube state updates
- Step-by-step solving visualization

### 🎮 **User-Friendly Interface**
- Intuitive GUI with Tkinter
- Scramble generation and custom move input
- Multiple solving modes (complete solve vs. step-by-step)
- Comprehensive output logging

## Installation

### Prerequisites
- Python 3.7 or higher
- Tkinter (included with Python)

### Basic Installation
```bash
git clone <repository-url>
cd rubics-cube
python main.py
```

### Optional: Install Kociemba for Optimal 3x3 Solving
```bash
pip install kociemba
```

## Quick Start

1. **Launch the application:**
   ```bash
   python main.py
   ```

2. **Select cube size** (2x2, 3x3, or 4x4)

3. **Scramble the cube** or input custom moves

4. **Choose solving method:**
   - Auto: Best method for cube size
   - Kociemba: Optimal solutions (3x3 only)
   - Beginner: Step-by-step method
   - Size-specific: Specialized algorithms

5. **Solve:** Complete solve or step-by-step

## File Structure

```
rubics-cube/
├── main.py                 # Main GUI application
├── cube.py                 # Core cube data structure and move engine
├── cube_utils.py           # Utility functions and state conversion
├── visualizer.py           # 2D Tkinter cube visualizer
├── kociemba_solver.py      # Kociemba algorithm integration
├── beginner_solver.py      # Layer-by-layer beginner method
├── multi_size_solver.py    # 2x2 and 4x4 specialized solvers
├── requirements.txt        # Dependencies
└── README.md              # This file
```

## Usage Examples

### Basic Cube Operations
```python
from cube import Cube
from cube_utils import *

# Create a 3x3 cube
cube = Cube(3)

# Apply moves
cube.apply_move("R U R' U'")

# Scramble
scramble_moves = cube.scramble(25)
print(f"Scramble: {' '.join(scramble_moves)}")

# Check if solved
print(f"Solved: {cube.is_solved()}")
```

### Using Kociemba Solver
```python
from kociemba_solver import KociembaSolver

solver = KociembaSolver()
if solver.is_available():
    solution, time, success = solver.solve(cube)
    if success:
        print(f"Solution: {' '.join(solution)} ({len(solution)} moves)")
```

### Step-by-Step Solving
```python
from beginner_solver import BeginnerSolver

solver = BeginnerSolver()
while not cube.is_solved():
    result = solver.solve_next_step(cube)
    print(f"Step: {result['step']}")
    print(f"Moves: {' '.join(result['moves'])}")
```

## Move Notation

The application uses standard Rubik's Cube notation:

### Basic Moves
- **R, L, U, D, F, B**: Face turns (90° clockwise)
- **R', L', U', D', F', B'**: Counter-clockwise turns
- **R2, L2, U2, D2, F2, B2**: Double turns (180°)

### Advanced Moves
- **Rw, Lw, Uw, Dw, Fw, Bw**: Wide turns (two layers)
- **M, E, S**: Slice moves (middle layers)

### Examples
- `R U R' U'`: Sexy move
- `F R U R' U' F'`: Yellow cross algorithm
- `R U R' F' R U R' U' R' F R2 U' R'`: T-permutation

## Algorithms Implemented

### 3x3 Cube
1. **Kociemba Two-Phase Algorithm**
   - Phase 1: Reduce to subgroup H
   - Phase 2: Solve within subgroup H
   - Guarantees ≤20 move solutions

2. **Beginner Layer-by-Layer**
   - White cross formation
   - White corners completion
   - Middle layer edges
   - Yellow cross (OLL)
   - Yellow corners positioning and orientation
   - Final layer edge permutation

### 2x2 Cube (Ortega Method)
1. Solve one face (usually white)
2. Orient last layer (OLL)
3. Permute last layer (PLL)

### 4x4 Cube (Reduction Method)
1. Solve centers
2. Pair up edges
3. Solve like 3x3
4. Handle parity cases (OLL/PLL parity)

## Technical Details

### Cube Representation
- **Faces**: U(p), D(own), F(ront), B(ack), L(eft), R(ight)
- **Colors**: White, Yellow, Green, Blue, Orange, Red
- **Data Structure**: 2D arrays for each face
- **Size Support**: NxN cubes (tested with 2x2, 3x3, 4x4)

### Move Engine
- Complete face turn implementation
- Edge and corner piece tracking
- Wide move and slice move support
- Move sequence parsing and optimization

### State Conversion
- Facelet string format for Kociemba integration
- Cube state validation
- Move sequence analysis and reversal

## Performance

### Solving Times (approximate)
- **2x2**: < 1 second, 8-12 moves average
- **3x3 Kociemba**: < 1 second, ≤20 moves optimal
- **3x3 Beginner**: Instant steps, 50-60 moves average
- **4x4**: Variable, 60-80 moves average

### Memory Usage
- Minimal memory footprint
- Efficient cube state representation
- Optional Kociemba lookup tables (~100MB)

## Troubleshooting

### Kociemba Installation Issues
If `pip install kociemba` fails:
1. Update pip: `pip install --upgrade pip`
2. Try conda: `conda install -c conda-forge kociemba`
3. Use fallback solvers (application works without Kociemba)

### GUI Issues
- Ensure Tkinter is installed (usually included with Python)
- On Linux: `sudo apt-get install python3-tk`
- On macOS: Tkinter should be included with Python

### Performance Issues
- For large cubes or complex scrambles, solving may take longer
- Use threading for non-blocking GUI operations
- Consider reducing scramble length for faster solving

## Contributing

Contributions are welcome! Areas for improvement:
- Additional cube sizes (5x5, 6x6, etc.)
- More solving algorithms (CFOP, Roux, ZZ)
- 3D visualization
- Solve optimization and analysis
- Mobile/web interface

## License

This project is open source. Feel free to use, modify, and distribute.

## Acknowledgments

- **Herbert Kociemba**: Two-phase algorithm
- **Rubik's Cube Community**: Algorithm development and notation standards
- **Python Community**: Excellent libraries and documentation

---

**Happy Cubing! 🎲**
