"""
Rubik's Cube Solver - Main Application
Interactive GUI application with multiple solving methods
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time

from cube import Cube
from visualizer import CubeVisualizer
from cube_utils import *
from kociemba_solver import KociembaSolver, create_solver
from beginner_solver import <PERSON>ginnerSolver
from multi_size_solver import create_solver_for_size, get_size_specific_info
from simple_working_solver import SimpleWorkingSolver


class RubiksCubeSolverApp:
    """Main application window with all solving features."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Ultimate Rubik's Cube Solver")
        self.root.geometry("1200x800")
        
        # Initialize components
        self.cube = Cube(3)
        self.kociemba_solver = KociembaSolver()
        self.beginner_solver = BeginnerSolver()
        self.simple_solver = SimpleWorkingSolver()  # Add working solver
        self.current_solver = self.simple_solver  # Default to working solver
        
        # Create GUI
        self.create_menu()
        self.create_main_interface()
        
        # Initialize display
        self.update_cube_display()
    
    def create_menu(self):
        """Create the application menu bar."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Cube", command=self.new_cube)
        file_menu.add_command(label="Reset Cube", command=self.reset_cube)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # Cube menu
        cube_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Cube", menu=cube_menu)
        cube_menu.add_command(label="2x2 Cube", command=lambda: self.change_cube_size(2))
        cube_menu.add_command(label="3x3 Cube", command=lambda: self.change_cube_size(3))
        cube_menu.add_command(label="4x4 Cube", command=lambda: self.change_cube_size(4))
        
        # Solver menu
        solver_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Solver", menu=solver_menu)
        solver_menu.add_command(label="Kociemba (Optimal)", command=self.use_kociemba_solver)
        solver_menu.add_command(label="Beginner Method", command=self.use_beginner_solver)
        solver_menu.add_command(label="Auto-detect", command=self.use_auto_solver)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
        help_menu.add_command(label="Instructions", command=self.show_instructions)
    
    def create_main_interface(self):
        """Create the main application interface."""
        # Create main paned window
        main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel - Cube visualization
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=2)
        
        # Cube display area
        self.cube_frame = ttk.LabelFrame(left_frame, text="Cube Visualization")
        self.cube_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create embedded visualizer
        self.create_cube_display()
        
        # Right panel - Controls
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=1)
        
        # Create control panels
        self.create_cube_controls(right_frame)
        self.create_solver_controls(right_frame)
        self.create_output_panel(right_frame)
    
    def create_cube_display(self):
        """Create the cube visualization display."""
        # Canvas for cube display
        self.canvas = tk.Canvas(self.cube_frame, bg='lightgray', width=500, height=400)
        self.canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Status bar
        self.status_frame = ttk.Frame(self.cube_frame)
        self.status_frame.pack(fill=tk.X, padx=5, pady=2)
        
        self.status_label = ttk.Label(self.status_frame, text="Status: Solved")
        self.status_label.pack(side=tk.LEFT)
        
        self.cube_info_label = ttk.Label(self.status_frame, text="3x3 Cube")
        self.cube_info_label.pack(side=tk.RIGHT)
    
    def create_cube_controls(self, parent):
        """Create cube manipulation controls."""
        control_frame = ttk.LabelFrame(parent, text="Cube Controls")
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Size selection
        size_frame = ttk.Frame(control_frame)
        size_frame.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(size_frame, text="Size:").pack(side=tk.LEFT)
        self.size_var = tk.StringVar(value="3")
        size_combo = ttk.Combobox(size_frame, textvariable=self.size_var, 
                                 values=["2", "3", "4"], width=5, state="readonly")
        size_combo.pack(side=tk.LEFT, padx=5)
        size_combo.bind("<<ComboboxSelected>>", self.on_size_change)
        
        ttk.Button(size_frame, text="New Cube", command=self.new_cube).pack(side=tk.RIGHT)
        
        # Scramble controls
        scramble_frame = ttk.Frame(control_frame)
        scramble_frame.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(scramble_frame, text="Scramble:").pack(side=tk.LEFT)
        self.scramble_length = tk.StringVar(value="25")
        ttk.Entry(scramble_frame, textvariable=self.scramble_length, width=5).pack(side=tk.LEFT, padx=5)
        ttk.Button(scramble_frame, text="Scramble", command=self.scramble_cube).pack(side=tk.LEFT, padx=2)
        ttk.Button(scramble_frame, text="Reset", command=self.reset_cube).pack(side=tk.RIGHT)
        
        # Move input
        move_frame = ttk.Frame(control_frame)
        move_frame.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(move_frame, text="Moves:").pack(anchor=tk.W)
        self.move_entry = tk.Entry(move_frame)
        self.move_entry.pack(fill=tk.X, pady=2)
        self.move_entry.bind("<Return>", lambda e: self.apply_moves())
        
        move_buttons = ttk.Frame(move_frame)
        move_buttons.pack(fill=tk.X, pady=2)
        ttk.Button(move_buttons, text="Apply", command=self.apply_moves).pack(side=tk.LEFT)
        ttk.Button(move_buttons, text="Clear", command=self.clear_moves).pack(side=tk.RIGHT)
    
    def create_solver_controls(self, parent):
        """Create solver selection and control panel."""
        solver_frame = ttk.LabelFrame(parent, text="Solver Controls")
        solver_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Solver selection
        solver_select_frame = ttk.Frame(solver_frame)
        solver_select_frame.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(solver_select_frame, text="Method:").pack(side=tk.LEFT)
        self.solver_var = tk.StringVar(value="Auto")
        solver_combo = ttk.Combobox(solver_select_frame, textvariable=self.solver_var,
                                   values=["Auto", "Simple", "Kociemba", "Beginner", "Size-specific"],
                                   state="readonly")
        solver_combo.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        solver_combo.bind("<<ComboboxSelected>>", self.on_solver_change)
        
        # Solve buttons
        solve_buttons = ttk.Frame(solver_frame)
        solve_buttons.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(solve_buttons, text="Solve Complete", command=self.solve_complete).pack(side=tk.LEFT, padx=2)
        ttk.Button(solve_buttons, text="Next Step", command=self.solve_next_step).pack(side=tk.LEFT, padx=2)
        ttk.Button(solve_buttons, text="Reset Solver", command=self.reset_solver).pack(side=tk.RIGHT, padx=2)
        
        # Solver info
        self.solver_info_label = ttk.Label(solver_frame, text="Ready to solve")
        self.solver_info_label.pack(fill=tk.X, padx=5, pady=2)
    
    def create_output_panel(self, parent):
        """Create output and logging panel."""
        output_frame = ttk.LabelFrame(parent, text="Output")
        output_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Output text area
        self.output_text = scrolledtext.ScrolledText(output_frame, height=15, width=40)
        self.output_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Clear button
        ttk.Button(output_frame, text="Clear Output", command=self.clear_output).pack(pady=2)
        
        # Initial message
        self.log("Welcome to the Ultimate Rubik's Cube Solver!")
        self.log("Select a cube size, scramble, and choose a solving method.")
    
    def update_cube_display(self):
        """Update the cube visualization."""
        self.canvas.delete("all")
        
        # Draw cube using simplified net layout
        size = self.cube.size
        sticker_size = min(30, 400 // (size * 4))  # Adaptive size
        gap = 2
        
        # Calculate positions for net layout
        face_width = size * sticker_size + (size - 1) * gap
        start_x = 50
        start_y = 50
        
        face_positions = {
            'U': (start_x + face_width + gap, start_y),
            'L': (start_x, start_y + face_width + gap),
            'F': (start_x + face_width + gap, start_y + face_width + gap),
            'R': (start_x + 2 * (face_width + gap), start_y + face_width + gap),
            'B': (start_x + 3 * (face_width + gap), start_y + face_width + gap),
            'D': (start_x + face_width + gap, start_y + 2 * (face_width + gap))
        }
        
        # Color mapping
        colors = {
            'W': '#FFFFFF', 'Y': '#FFFF00', 'G': '#00FF00',
            'B': '#0000FF', 'O': '#FF8000', 'R': '#FF0000'
        }
        
        # Draw each face
        for face, (x, y) in face_positions.items():
            # Face label
            self.canvas.create_text(x + face_width//2, y - 15, text=face, font=('Arial', 10, 'bold'))
            
            # Draw stickers
            for i in range(size):
                for j in range(size):
                    color_code = self.cube.faces[face][i][j]
                    color = colors.get(color_code, '#CCCCCC')
                    
                    x1 = x + j * (sticker_size + gap)
                    y1 = y + i * (sticker_size + gap)
                    x2 = x1 + sticker_size
                    y2 = y1 + sticker_size
                    
                    self.canvas.create_rectangle(x1, y1, x2, y2, fill=color, outline='black', width=1)
        
        # Update status
        self.update_status()
    
    def update_status(self):
        """Update status labels."""
        if self.cube.is_solved():
            self.status_label.config(text="Status: Solved")
        else:
            self.status_label.config(text="Status: Scrambled")
        
        self.cube_info_label.config(text=f"{self.cube.size}x{self.cube.size} Cube")
    
    def log(self, message):
        """Add message to output log."""
        timestamp = time.strftime("%H:%M:%S")
        self.output_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.output_text.see(tk.END)
    
    # Event handlers
    def on_size_change(self, event=None):
        """Handle cube size change."""
        size = int(self.size_var.get())
        self.change_cube_size(size)
    
    def on_solver_change(self, event=None):
        """Handle solver method change."""
        method = self.solver_var.get()
        if method == "Kociemba":
            self.use_kociemba_solver()
        elif method == "Beginner":
            self.use_beginner_solver()
        elif method == "Simple":
            self.use_simple_solver()
        elif method == "Size-specific":
            self.use_auto_solver()
        else:  # Auto
            self.use_auto_solver()
    
    def change_cube_size(self, size):
        """Change cube size and reset."""
        self.cube = Cube(size)
        self.size_var.set(str(size))
        self.update_cube_display()
        self.log(f"Changed to {size}x{size} cube")
    
    def new_cube(self):
        """Create new cube of current size."""
        size = int(self.size_var.get())
        self.cube = Cube(size)
        self.update_cube_display()
        self.log(f"Created new {size}x{size} cube")
    
    def reset_cube(self):
        """Reset cube to solved state."""
        self.cube.reset()
        self.update_cube_display()
        self.log("Cube reset to solved state")
    
    def scramble_cube(self):
        """Apply random scramble."""
        try:
            length = int(self.scramble_length.get())
            scramble_moves = self.cube.scramble(length)
            self.update_cube_display()
            self.log(f"Applied scramble: {' '.join(scramble_moves)}")
        except ValueError:
            messagebox.showerror("Error", "Invalid scramble length")
    
    def apply_moves(self):
        """Apply moves from entry field."""
        move_text = self.move_entry.get().strip()
        if not move_text:
            return
        
        try:
            moves = parse_move_sequence(move_text)
            for move in moves:
                self.cube.apply_move(move)
            self.update_cube_display()
            self.log(f"Applied moves: {' '.join(moves)}")
            self.move_entry.delete(0, tk.END)
        except Exception as e:
            messagebox.showerror("Error", f"Invalid moves: {str(e)}")
    
    def clear_moves(self):
        """Clear move entry field."""
        self.move_entry.delete(0, tk.END)
    
    def use_kociemba_solver(self):
        """Switch to Kociemba solver."""
        self.current_solver = self.kociemba_solver
        self.solver_var.set("Kociemba")
        if self.kociemba_solver.is_available():
            self.solver_info_label.config(text="Kociemba solver ready (optimal solutions)")
        else:
            self.solver_info_label.config(text="Kociemba not available - install with: pip install kociemba")
    
    def use_beginner_solver(self):
        """Switch to beginner solver."""
        self.current_solver = self.beginner_solver
        self.solver_var.set("Beginner")
        self.solver_info_label.config(text="Beginner solver ready (step-by-step)")

    def use_simple_solver(self):
        """Switch to simple solver."""
        self.current_solver = self.simple_solver
        self.solver_var.set("Simple")
        self.solver_info_label.config(text="Simple solver ready (works for small scrambles)")
    
    def use_auto_solver(self):
        """Use automatic solver selection."""
        size = self.cube.size
        if size == 3 and self.kociemba_solver.is_available():
            self.current_solver = self.kociemba_solver
            self.solver_info_label.config(text="Auto: Using Kociemba for 3x3")
        elif size == 3:
            self.current_solver = self.simple_solver  # Use simple solver instead of beginner
            self.solver_info_label.config(text="Auto: Using Simple solver for 3x3")
        else:
            self.current_solver = create_solver_for_size(size)
            info = get_size_specific_info(size)
            self.solver_info_label.config(text=f"Auto: Using {info['method']} for {size}x{size}")
    
    def solve_complete(self):
        """Solve the entire cube."""
        if self.cube.is_solved():
            self.log("Cube is already solved!")
            return
        
        if not self.current_solver:
            self.use_auto_solver()
        
        self.log("Solving cube...")
        
        # Run solver in separate thread to avoid GUI freezing
        def solve_thread():
            try:
                if hasattr(self.current_solver, 'solve'):
                    result = self.current_solver.solve(self.cube)
                    if isinstance(result, tuple):  # Kociemba returns tuple
                        moves, solve_time, success = result
                        if success:
                            self.root.after(0, lambda: self.on_solve_complete(moves, solve_time))
                        else:
                            self.root.after(0, lambda: self.log("Failed to find solution"))
                    else:  # Other solvers return dict
                        self.root.after(0, lambda: self.on_solve_complete_dict(result))
                else:
                    self.root.after(0, lambda: self.log("Solver not available"))
            except Exception as e:
                self.root.after(0, lambda: self.log(f"Solve error: {str(e)}"))
        
        threading.Thread(target=solve_thread, daemon=True).start()
    
    def on_solve_complete(self, moves, solve_time):
        """Handle completion of Kociemba solve."""
        self.update_cube_display()
        self.log(f"Solved in {solve_time:.3f} seconds!")
        self.log(f"Solution: {' '.join(moves)} ({len(moves)} moves)")
    
    def on_solve_complete_dict(self, result):
        """Handle completion of other solvers."""
        self.update_cube_display()
        if result.get("success"):
            self.log(f"Solved! Used {result.get('total_moves', 0)} moves")
            if "moves" in result:
                self.log(f"Solution: {' '.join(result['moves'])}")
        else:
            self.log("Failed to solve")
            if "error" in result:
                self.log(f"Error: {result['error']}")
    
    def solve_next_step(self):
        """Solve next step (for step-by-step solvers)."""
        if hasattr(self.current_solver, 'solve_next_step'):
            result = self.current_solver.solve_next_step(self.cube)
            self.update_cube_display()
            self.log(f"Step: {result.get('step', 'Unknown')}")
            if result.get('moves'):
                self.log(f"Moves: {' '.join(result['moves'])}")
        else:
            self.log("Current solver doesn't support step-by-step solving")
    
    def reset_solver(self):
        """Reset solver state."""
        if hasattr(self.current_solver, 'reset'):
            self.current_solver.reset()
        self.log("Solver reset")
    
    def clear_output(self):
        """Clear output text."""
        self.output_text.delete(1.0, tk.END)
    
    def show_about(self):
        """Show about dialog."""
        about_text = """Ultimate Rubik's Cube Solver

Features:
• Support for 2x2, 3x3, and 4x4 cubes
• Multiple solving algorithms
• Interactive visualization
• Step-by-step solving
• Optimal solutions with Kociemba

Created with Python and Tkinter"""
        messagebox.showinfo("About", about_text)
    
    def show_instructions(self):
        """Show instructions dialog."""
        instructions = """Instructions:

1. Select cube size (2x2, 3x3, or 4x4)
2. Scramble the cube or apply custom moves
3. Choose a solving method:
   - Kociemba: Optimal solutions for 3x3
   - Beginner: Step-by-step method
   - Auto: Best method for cube size
4. Click 'Solve Complete' or 'Next Step'
5. Watch the solution in the output panel

Move Notation:
R, L, U, D, F, B = Face turns
' = Counter-clockwise (e.g., R')
2 = Double turn (e.g., R2)
w = Wide turn (e.g., Rw)"""
        messagebox.showinfo("Instructions", instructions)
    
    def run(self):
        """Start the application."""
        self.root.mainloop()


def main():
    """Main entry point."""
    app = RubiksCubeSolverApp()
    app.run()


if __name__ == "__main__":
    main()
