"""
Cube State Utilities
Provides conversion, validation, and analysis functions for Rubik's Cube states
"""

from cube import Cube
import re


def get_facelet_string(cube):
    """
    Convert cube state to 54-character facelet string for Kociemba algorithm.
    Order: URFDLB (Up, Right, Front, Down, Left, Back)
    Each face is read row by row, left to right, top to bottom.
    """
    if cube.size != 3:
        raise ValueError("Facelet string conversion only supports 3x3 cubes")
    
    # Kociemba uses different color mapping: U=U, R=R, F=F, D=D, L=L, B=B
    # Convert our color codes to Kociemba format
    color_map = {
        'W': 'U',  # White -> Up
        'R': 'R',  # Red -> Right  
        'G': 'F',  # Green -> Front
        'Y': 'D',  # Yellow -> Down
        'O': 'L',  # Orange -> Left
        'B': 'B'   # Blue -> Back
    }
    
    facelet_order = ['U', 'R', 'F', 'D', 'L', 'B']
    facelet_string = ''
    
    for face in facelet_order:
        for row in cube.faces[face]:
            for sticker in row:
                facelet_string += color_map[sticker]
    
    return facelet_string


def facelet_string_to_cube(facelet_string):
    """
    Convert 54-character facelet string back to Cube object.
    """
    if len(facelet_string) != 54:
        raise ValueError("Facelet string must be exactly 54 characters")
    
    # Reverse color mapping
    color_map = {
        'U': 'W',  # Up -> White
        'R': 'R',  # Right -> Red
        'F': 'G',  # Front -> Green  
        'D': 'Y',  # Down -> Yellow
        'L': 'O',  # Left -> Orange
        'B': 'B'   # Back -> Blue
    }
    
    cube = Cube(3)
    facelet_order = ['U', 'R', 'F', 'D', 'L', 'B']
    
    pos = 0
    for face in facelet_order:
        for i in range(3):
            for j in range(3):
                cube.faces[face][i][j] = color_map[facelet_string[pos]]
                pos += 1
    
    return cube


def parse_move_sequence(move_string):
    """
    Parse a string of moves into individual move tokens.
    Handles standard notation: R, R', R2, Rw, Rw', Rw2, etc.
    """
    # Regular expression to match move notation
    move_pattern = r"([RLUDFBMES]w?[2']?)"
    moves = re.findall(move_pattern, move_string.upper())
    return moves


def apply_move_sequence(cube, move_string):
    """
    Apply a sequence of moves to a cube.
    Returns the list of individual moves applied.
    """
    moves = parse_move_sequence(move_string)
    for move in moves:
        cube.apply_move(move)
    return moves


def reverse_move_sequence(moves):
    """
    Generate the reverse of a move sequence.
    Useful for undoing scrambles or solutions.
    """
    reversed_moves = []
    for move in reversed(moves):
        if move.endswith("'"):
            # Remove prime
            reversed_moves.append(move[:-1])
        elif move.endswith("2"):
            # Double move is its own reverse
            reversed_moves.append(move)
        else:
            # Add prime
            reversed_moves.append(move + "'")
    return reversed_moves


def count_moves(move_string):
    """Count the number of individual moves in a move sequence."""
    moves = parse_move_sequence(move_string)
    return len(moves)


def optimize_move_sequence(moves):
    """
    Basic move sequence optimization.
    Combines consecutive moves on the same face and removes redundant moves.
    """
    if not moves:
        return []
    
    optimized = []
    i = 0
    
    while i < len(moves):
        current_move = moves[i]
        face = current_move[0]
        
        # Count consecutive moves on the same face
        count = 0
        j = i
        while j < len(moves) and moves[j][0] == face:
            move = moves[j]
            if move.endswith("'"):
                count -= 1
            elif move.endswith("2"):
                count += 2
            else:
                count += 1
            j += 1
        
        # Normalize count to 0-3 range
        count = count % 4
        
        # Add optimized move
        if count == 1:
            optimized.append(face)
        elif count == 2:
            optimized.append(face + "2")
        elif count == 3:
            optimized.append(face + "'")
        # count == 0 means no move needed
        
        i = j
    
    return optimized


def validate_cube_state(cube):
    """
    Validate that a cube state is theoretically solvable.
    Checks for correct number of each color and basic parity.
    """
    if cube.size != 3:
        return True, "Validation only implemented for 3x3 cubes"
    
    # Count colors
    color_counts = {'W': 0, 'R': 0, 'G': 0, 'Y': 0, 'O': 0, 'B': 0}
    
    for face in cube.faces:
        for row in cube.faces[face]:
            for sticker in row:
                if sticker in color_counts:
                    color_counts[sticker] += 1
                else:
                    return False, f"Invalid color: {sticker}"
    
    # Each color should appear exactly 9 times
    for color, count in color_counts.items():
        if count != 9:
            return False, f"Color {color} appears {count} times, expected 9"
    
    return True, "Valid cube state"


def get_cube_signature(cube):
    """
    Generate a unique signature for the cube state.
    Useful for detecting repeated states or caching.
    """
    signature = ""
    for face in ['U', 'R', 'F', 'D', 'L', 'B']:
        for row in cube.faces[face]:
            signature += ''.join(row)
    return signature


def cube_distance(cube1, cube2):
    """
    Calculate the number of different stickers between two cubes.
    Useful for measuring similarity or progress.
    """
    if cube1.size != cube2.size:
        raise ValueError("Cubes must be the same size")
    
    differences = 0
    for face in cube1.faces:
        for i in range(cube1.size):
            for j in range(cube1.size):
                if cube1.faces[face][i][j] != cube2.faces[face][i][j]:
                    differences += 1
    
    return differences


def generate_scramble(length=25, avoid_redundant=True):
    """
    Generate a random scramble sequence.
    If avoid_redundant is True, prevents consecutive moves on the same face.
    """
    import random
    
    faces = ['R', 'L', 'U', 'D', 'F', 'B']
    modifiers = ['', "'", '2']
    
    scramble = []
    last_face = None
    
    for _ in range(length):
        if avoid_redundant and last_face:
            available_faces = [f for f in faces if f != last_face]
        else:
            available_faces = faces
        
        face = random.choice(available_faces)
        modifier = random.choice(modifiers)
        move = face + modifier
        
        scramble.append(move)
        last_face = face
    
    return scramble


def format_move_sequence(moves, moves_per_line=10):
    """
    Format a move sequence for display with line breaks.
    """
    if not moves:
        return ""
    
    lines = []
    for i in range(0, len(moves), moves_per_line):
        line = ' '.join(moves[i:i + moves_per_line])
        lines.append(line)
    
    return '\n'.join(lines)


# Predefined useful scrambles and patterns
SUPERFLIP = "R L U2 F U D F2 R2 U' D' R2 F L' B2 U2 F2"
CHECKERBOARD = "M2 E2 S2"
CUBE_IN_CUBE = "F L F U' R U F2 L2 U' L' B D' B' L2 U"

# Common algorithm patterns
SEXY_MOVE = "R U R' U'"
SLEDGEHAMMER = "R' F R F'"
T_PERM = "R U R' F' R U R' U' R' F R2 U' R'"
