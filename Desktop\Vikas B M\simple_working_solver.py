"""
Simple Working Solver
A basic solver that actually works for small scrambles
"""

from cube import Cube
from cube_utils import parse_move_sequence
import time
import random


class SimpleWorkingSolver:
    """
    A simple solver that uses brute force for small scrambles
    and random moves for larger ones.
    """
    
    def __init__(self):
        self.basic_moves = ['R', "R'", 'L', "L'", 'U', "U'", 'D', "D'", 'F', "F'", 'B', "B'"]
    
    def solve(self, cube):
        """
        Solve the cube using brute force for small scrambles.
        
        Returns:
            tuple: (solution_moves, solve_time, success)
        """
        start_time = time.time()
        
        if cube.is_solved():
            return [], 0, True
        
        # Try brute force for small scrambles
        solution = self._brute_force_solve(cube)
        if solution:
            solve_time = time.time() - start_time
            return solution, solve_time, True
        
        # If brute force fails, try random moves
        solution = self._random_solve(cube)
        solve_time = time.time() - start_time
        
        # Check if solved
        test_cube = cube.copy()
        for move in solution:
            test_cube.apply_move(move)
        
        return solution, solve_time, test_cube.is_solved()
    
    def _brute_force_solve(self, cube):
        """Try all possible move sequences up to a certain depth."""
        max_depth = 6  # Only try up to 6 moves
        
        def search(current_cube, moves, depth):
            if current_cube.is_solved():
                return moves
            
            if depth >= max_depth:
                return None
            
            for move in self.basic_moves:
                # Skip redundant moves
                if moves and move[0] == moves[-1][0]:
                    continue
                
                # Apply move
                new_cube = current_cube.copy()
                new_cube.apply_move(move)
                
                # Recursive search
                result = search(new_cube, moves + [move], depth + 1)
                if result:
                    return result
            
            return None
        
        return search(cube.copy(), [], 0)
    
    def _random_solve(self, cube):
        """Try random moves to solve the cube."""
        max_attempts = 1000
        best_distance = float('inf')
        best_moves = []
        
        for _ in range(max_attempts):
            test_cube = cube.copy()
            moves = []
            
            for _ in range(20):  # Try up to 20 random moves
                move = random.choice(self.basic_moves)
                test_cube.apply_move(move)
                moves.append(move)
                
                if test_cube.is_solved():
                    return moves
                
                # Track best attempt
                distance = self._cube_distance(test_cube)
                if distance < best_distance:
                    best_distance = distance
                    best_moves = moves.copy()
        
        return best_moves
    
    def _cube_distance(self, cube):
        """Calculate how far cube is from solved state."""
        solved_cube = Cube(cube.size)
        distance = 0
        
        for face in cube.faces:
            for i in range(cube.size):
                for j in range(cube.size):
                    if cube.faces[face][i][j] != solved_cube.faces[face][i][j]:
                        distance += 1
        
        return distance


def demo_simple_solver():
    """Demonstrate the simple solver."""
    print("=== Simple Working Solver Demo ===")
    
    # Test with very simple scramble
    cube = Cube(3)
    print("Original state:", cube.is_solved())
    
    # Apply simple scramble
    simple_scramble = ["R", "U"]
    for move in simple_scramble:
        cube.apply_move(move)
    print(f"Applied scramble: {' '.join(simple_scramble)}")
    print("Scrambled state:", cube.is_solved())
    
    # Try to solve
    solver = SimpleWorkingSolver()
    solution, solve_time, success = solver.solve(cube)
    
    print(f"Solve attempt: {'Success' if success else 'Failed'}")
    print(f"Solution length: {len(solution)} moves")
    print(f"Solve time: {solve_time:.3f} seconds")
    if solution:
        print(f"Solution: {' '.join(solution)}")
    
    return success


if __name__ == "__main__":
    demo_simple_solver()
