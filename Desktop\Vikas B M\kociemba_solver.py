"""
Kociemba Algorithm Integration
Optimal solving for 3x3 Rubik's Cube using the two-phase algorithm
"""

from cube import Cube
from cube_utils import get_facelet_string, parse_move_sequence
import time


class KociembaSolver:
    """
    Wrapper for Kociemba two-phase algorithm.
    Provides optimal solutions for 3x3 cubes.
    """
    
    def __init__(self):
        self.kociemba_available = False
        self.kociemba = None
        self._try_import_kociemba()
    
    def _try_import_kociemba(self):
        """Try to import the kociemba library."""
        try:
            import kociemba
            self.kociemba = kociemba
            self.kociemba_available = True
            print("Kociemba library loaded successfully")
        except ImportError:
            print("Kociemba library not found. Install with: pip install kociemba")
            print("Falling back to manual installation instructions...")
            self.kociemba_available = False
    
    def is_available(self):
        """Check if Kociemba solver is available."""
        return self.kociemba_available
    
    def solve(self, cube, max_depth=24, timeout=10):
        """
        Solve a 3x3 cube using <PERSON><PERSON><PERSON>'s algorithm.
        
        Args:
            cube: Cube object to solve
            max_depth: Maximum search depth (default 24)
            timeout: Timeout in seconds (default 10)
        
        Returns:
            tuple: (solution_moves, solve_time, success)
        """
        if not self.kociemba_available:
            return [], 0, False
        
        if cube.size != 3:
            raise ValueError("Kociemba solver only works with 3x3 cubes")
        
        if cube.is_solved():
            return [], 0, True
        
        try:
            # Convert cube to facelet string
            facelet_string = get_facelet_string(cube)
            
            # Solve using Kociemba
            start_time = time.time()
            solution_string = self.kociemba.solve(facelet_string, max_depth, timeout)
            solve_time = time.time() - start_time
            
            # Check if solution was found
            if solution_string.startswith("Error"):
                return [], solve_time, False
            
            # Parse solution moves
            solution_moves = parse_move_sequence(solution_string)
            
            return solution_moves, solve_time, True
            
        except Exception as e:
            print(f"Kociemba solve error: {e}")
            return [], 0, False
    
    def solve_and_apply(self, cube, max_depth=24, timeout=10):
        """
        Solve cube and apply the solution moves.
        
        Returns:
            tuple: (solution_moves, solve_time, success)
        """
        solution_moves, solve_time, success = self.solve(cube, max_depth, timeout)
        
        if success:
            for move in solution_moves:
                cube.apply_move(move)
        
        return solution_moves, solve_time, success
    
    def verify_solution(self, original_cube, solution_moves):
        """
        Verify that a solution actually solves the cube.
        
        Args:
            original_cube: The scrambled cube state
            solution_moves: List of moves to verify
        
        Returns:
            bool: True if solution is correct
        """
        # Create a copy and apply solution
        test_cube = original_cube.copy()
        for move in solution_moves:
            test_cube.apply_move(move)
        
        return test_cube.is_solved()
    
    def get_installation_instructions(self):
        """Get instructions for installing Kociemba library."""
        instructions = """
To install the Kociemba library:

1. Using pip (recommended):
   pip install kociemba

2. If pip fails, try:
   pip install --upgrade pip
   pip install kociemba

3. For conda users:
   conda install -c conda-forge kociemba

4. Manual installation:
   - Download from: https://github.com/muodov/kociemba
   - Follow the repository instructions

Note: The library includes pre-computed lookup tables
and may take a moment to initialize on first use.
"""
        return instructions


class FallbackSolver:
    """
    Simple fallback solver when Kociemba is not available.
    Uses basic layer-by-layer approach (not optimal).
    """
    
    def __init__(self):
        pass
    
    def solve(self, cube, max_moves=100):
        """
        Attempt to solve using simple heuristics.
        This is not optimal and may not always find a solution.
        """
        if cube.is_solved():
            return [], 0, True
        
        # This is a placeholder - would need full implementation
        # For now, just return failure
        return [], 0, False
    
    def get_hint(self, cube):
        """
        Provide a hint for the next move in layer-by-layer solving.
        """
        if cube.is_solved():
            return "Cube is already solved!"
        
        # Analyze current state and suggest next step
        # This would need full implementation of layer-by-layer logic
        return "Use the beginner solver for step-by-step guidance"


def create_solver():
    """
    Factory function to create the best available solver.
    """
    kociemba_solver = KociembaSolver()
    if kociemba_solver.is_available():
        return kociemba_solver
    else:
        print("Kociemba not available, using fallback solver")
        return FallbackSolver()


def benchmark_solver(solver, num_scrambles=10, scramble_length=25):
    """
    Benchmark the solver performance.
    
    Args:
        solver: Solver instance to benchmark
        num_scrambles: Number of random scrambles to test
        scramble_length: Length of each scramble
    
    Returns:
        dict: Benchmark results
    """
    if not hasattr(solver, 'solve') or not solver.is_available():
        return {"error": "Solver not available"}
    
    results = {
        "total_scrambles": num_scrambles,
        "solved": 0,
        "failed": 0,
        "total_time": 0,
        "total_moves": 0,
        "avg_time": 0,
        "avg_moves": 0,
        "max_moves": 0,
        "min_moves": float('inf')
    }
    
    for i in range(num_scrambles):
        # Create and scramble cube
        cube = Cube(3)
        scramble_moves = cube.scramble(scramble_length)
        
        # Solve
        solution_moves, solve_time, success = solver.solve(cube)
        
        if success:
            results["solved"] += 1
            results["total_time"] += solve_time
            results["total_moves"] += len(solution_moves)
            results["max_moves"] = max(results["max_moves"], len(solution_moves))
            results["min_moves"] = min(results["min_moves"], len(solution_moves))
        else:
            results["failed"] += 1
        
        print(f"Scramble {i+1}/{num_scrambles}: {'Solved' if success else 'Failed'} "
              f"({len(solution_moves)} moves, {solve_time:.3f}s)")
    
    # Calculate averages
    if results["solved"] > 0:
        results["avg_time"] = results["total_time"] / results["solved"]
        results["avg_moves"] = results["total_moves"] / results["solved"]
    
    if results["min_moves"] == float('inf'):
        results["min_moves"] = 0
    
    return results


def demo_kociemba():
    """
    Demonstration of Kociemba solver usage.
    """
    print("=== Kociemba Solver Demo ===")
    
    # Create solver
    solver = KociembaSolver()
    
    if not solver.is_available():
        print("Kociemba library not available!")
        print(solver.get_installation_instructions())
        return
    
    # Create and scramble a cube
    cube = Cube(3)
    print("Original cube state: Solved")
    
    # Apply a known scramble
    scramble = "R U R' U R U2 R' F R U R' U' R' F R"
    scramble_moves = parse_move_sequence(scramble)
    for move in scramble_moves:
        cube.apply_move(move)
    
    print(f"Applied scramble: {scramble}")
    print(f"Scramble moves: {len(scramble_moves)}")
    
    # Solve the cube
    print("\nSolving...")
    solution_moves, solve_time, success = solver.solve(cube)
    
    if success:
        print(f"Solution found in {solve_time:.3f} seconds!")
        print(f"Solution: {' '.join(solution_moves)}")
        print(f"Solution length: {len(solution_moves)} moves")
        
        # Verify solution
        if solver.verify_solution(cube, solution_moves):
            print("Solution verified: ✓")
        else:
            print("Solution verification failed: ✗")
    else:
        print("Failed to find solution")


if __name__ == "__main__":
    demo_kociemba()
